from __future__ import annotations

import logging
from typing import Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import Chat<PERSON>ompletionClient

from app.tools.knowledge_tool_loader import KnowledgeToolLoader

from ....shared.config.base import Settings, get_settings
from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class KnowledgeBaseAgent:
    def __init__(self):
        """Initialize the KnowledgeBaseAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(
        self, organisation_id: Optional[str], user_id: Optional[str]
    ) -> bool:
        """
        Initialize the summary agent with model client.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1-mini-2025-04-14",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for KnowledgeBaseAgent")
                return False

            tools = await self._load_knowledge_tools(
                organization_id=organisation_id,
                user_id=user_id,
                agent_id=None,
                use_knowledge=True,
            )

            # Create the assistant agent (no external tools needed)
            self._agent = AssistantAgent(
                name="knowledge_base_agent",
                description="Uses tool to answer questions related to the user's internal company knowledge, such as people, policies, documents, tools, structure, or internal procedures.",
                model_client=self._model_client,
                tools=tools,
                reflect_on_tool_use=True,
                system_message=self._get_enhanced_system_message(),
                # model_client_stream=True,
            )

            self._is_initialized = True
            logger.info("KnowledgeBaseAgent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize KnowledgeBaseAgent: {str(e)}")
            self._is_initialized = False
            return False

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(
        cls, organisation_id: Optional[str], user_id: Optional[str]
    ) -> Optional["KnowledgeBaseAgent"]:
        """
        Create and initialize a KnowledgeBaseAgent instance.

        Returns:
            Optional[KnowledgeBaseAgent]: Initialized agent instance or None if failed
        """
        agent = cls()
        if await agent.initialize(organisation_id, user_id):
            return agent
        else:
            logger.error("Failed to create and initialize KnowledgeBaseAgent")
            return None

    async def _load_knowledge_tools(
        self,
        organization_id: Optional[str],
        user_id: Optional[str],
        agent_id: Optional[str],
        use_knowledge: Optional[bool],
    ):
        """Load knowledge tools if use_knowledge is True and organization_id is provided."""
        if not use_knowledge or not organization_id:
            return []

        try:

            knowledge_loader = KnowledgeToolLoader()
            all_tools = []

            # Always add the knowledge content retrieval tools
            content_tools = knowledge_loader.create_knowledge_content_tools(
                organization_id=organization_id,
                user_id=user_id,
                agent_id=agent_id,
            )
            all_tools.extend(content_tools)
            logger.info(f"Added {len(content_tools)} knowledge content tools")

            logger.info(
                f"Total knowledge tools loaded for organization "
                f"{organization_id}: {len(all_tools)}"
            )
            return all_tools

        except Exception as e:
            logger.error(
                f"Failed to load knowledge tools for organization "
                f"{organization_id}: {e}"
            )
            return []

    def _get_enhanced_system_message(self) -> str:
        """
        Get the enhanced system message for the summary agent.

        Returns:
            str: Comprehensive system message for summarization operations
        """
        return """
            You are an AI assistant that uses tools to answer questions related to the user's internal company knowledge, such as people, policies, documents, tools, structure, or internal procedures.

            CORE RESPONSIBILITIES:
            - Use tools to answer questions related to the user's internal company knowledge, such as people, policies, documents, tools, structure, or internal procedures.

            RESPONSE FORMATTING:
            - The response should be annotated with citations for all sources.
            - The results should be structured according to the following JSON format containing a list of sources:
            {
                "content": "Your well-structured response here",
                "sources": "The list of sources of the information",
                "file_names": "List of file names",
            }

            """
