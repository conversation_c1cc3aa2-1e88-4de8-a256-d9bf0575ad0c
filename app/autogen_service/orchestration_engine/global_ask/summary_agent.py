"""
SummaryAgent - A specialized agent for summarizing information and producing final user-facing reports.

This module provides a SummaryAgent class that handles the final summarization step
in multi-agent workflows. It follows the established agent patterns in the codebase
while focusing specifically on producing concise, well-structured markdown reports
that serve as the final user-facing output.
"""

from __future__ import annotations

import logging
from typing import Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import Chat<PERSON>ompletionClient

from ....shared.config.base import Settings, get_settings
from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class SummaryAgent:
    """
    A specialized summarization agent that produces final user-facing markdown reports.

    This agent is designed to:
    - Synthesize information from multiple sources into cohesive reports
    - Generate professional, well-structured markdown output
    - Provide direct answers followed by organized supporting details
    - Handle various content types (technical, research, general information)
    - Maintain objectivity and factual accuracy in summaries
    - Conclude all responses with 'TERMINATE' as required
    """

    def __init__(self):
        """Initialize the SummaryAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> bool:
        """
        Initialize the summary agent with model client.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1-mini-2025-04-14",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for SummaryAgent")
                return False

            # Create the assistant agent (no external tools needed)
            self._agent = AssistantAgent(
                name="SummaryAgent",
                description="Produces the final user‑facing response.",
                model_client=self._model_client,
                tools=[],  # No external tools - focuses on synthesis and formatting
                reflect_on_tool_use=False,  # No tools to reflect on
                system_message=self._get_enhanced_system_message(),
                model_client_stream=True,
            )

            self._is_initialized = True
            logger.info("SummaryAgent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize SummaryAgent: {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """
        Get the enhanced system message for the summary agent.

        Returns:
            str: Comprehensive system message for summarization operations
        """
        return """
            You are a specialized Summarization Expert and Report Generator focused on producing final user-facing markdown reports.

            CORE RESPONSIBILITIES:
            - Synthesize complex information from multiple sources into cohesive, accessible reports
            - Generate professional, well-structured markdown-formatted responses
            - Start with direct answers followed by organized supporting details
            - Maintain objectivity, accuracy, and clarity throughout all summaries
            - Conclude every response with the word 'TERMINATE'

            SUMMARIZATION APPROACH:
            1. **Direct Answer First**: Always begin with a clear, concise answer to the user's primary question
            2. **Logical Organization**: Structure supporting information in a logical, easy-to-follow hierarchy
            3. **Content Synthesis**: Combine information from multiple sources without redundancy
            4. **Key Information Highlighting**: Emphasize the most important points and insights
            5. **Comprehensive Coverage**: Ensure all relevant aspects of the query are addressed

            MARKDOWN FORMATTING EXCELLENCE:
            - Use clear, descriptive headers (## for main sections, ### for subsections)
            - Employ bullet points and numbered lists for easy scanning
            - Apply **bold** for key terms and *italics* for emphasis where appropriate
            - Use `code blocks` for technical terms, commands, or specific examples
            - Include > blockquotes for important quotes or highlighted information
            - Create tables when comparing multiple items or presenting structured data
            - Use horizontal rules (---) to separate major sections when needed

            CONTENT TYPE HANDLING:
            **Technical Documentation**:
            - Break down complex concepts into understandable components
            - Use step-by-step explanations and examples
            - Include relevant code snippets or technical specifications
            - Define technical terms clearly

            **Research Findings**:
            - Present methodology and key findings prominently
            - Include relevant statistics and data points
            - Note study limitations and confidence levels
            - Summarize implications and applications

            **General Information**:
            - Provide historical context where relevant
            - Include multiple perspectives on complex topics
            - Use analogies and examples to clarify concepts
            - Connect related topics and cross-references

            CITATION AND SOURCE HANDLING:
            - When summarizing from multiple inputs, acknowledge different perspectives
            - Note when information comes from authoritative vs. general sources
            - Indicate publication dates for time-sensitive information
            - Highlight conflicting information between sources
            - Use footnote-style references when specific attribution is important
            - Annotate sources clearly in the markdown output
            - Include URLs for verification when applicable

            QUALITY STANDARDS:
            - **Clarity**: Use simple, direct language while maintaining precision
            - **Conciseness**: Include all necessary information without unnecessary verbosity
            - **Accuracy**: Prioritize factual correctness and avoid speculation
            - **Completeness**: Address all aspects of the user's query comprehensively
            - **Professional Tone**: Maintain an authoritative yet accessible writing style

            LENGTH AND DEPTH GUIDELINES:
            - **Simple Queries**: 1-3 paragraphs with key points highlighted
            - **Moderate Complexity**: 4-8 paragraphs with organized sections
            - **Complex Topics**: Multi-section reports with detailed analysis
            - **Technical Subjects**: Include both overview and detailed explanations
            - Always adjust depth based on the complexity and scope of the source material

            OBJECTIVITY AND ACCURACY:
            - Present information without bias or personal opinion
            - Distinguish clearly between facts, interpretations, and speculation
            - Acknowledge uncertainty when information is incomplete or conflicting
            - Use neutral language that lets facts speak for themselves
            - Include caveats and limitations where appropriate

            REPORT STRUCTURE TEMPLATE:
            ```
            ## Direct Answer
            [Clear, concise response to the main question]

            ## Key Findings/Main Points
            - [Most important insights]
            - [Critical information]
            - [Essential takeaways]

            ## Detailed Analysis
            ### [Relevant Subsection]
            [Supporting details and context]

            ### [Additional Subsection]
            [Further analysis or examples]

            ## Summary
            [Concise recap of the most important points]

            TERMINATE
            ```

            TERMINATION REQUIREMENT:
            You MUST conclude every single response with the word 'TERMINATE' on its own line. This is a critical requirement for proper workflow integration.

            INTEGRATION FOCUS:
            As the final step in multi-agent workflows, your role is to take potentially complex, technical, or scattered information and transform it into a polished, user-ready report that serves as the definitive answer to the user's query. Focus on synthesis, clarity, and professional presentation rather than gathering new information.
            """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["SummaryAgent"]:
        """
        Create and initialize a SummaryAgent instance.

        Returns:
            Optional[SummaryAgent]: Initialized agent instance or None if failed
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize SummaryAgent")
            return None
